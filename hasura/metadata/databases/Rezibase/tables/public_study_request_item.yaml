table:
  name: study_request_item
  schema: public
object_relationships:
  - name: study_request
    using:
      foreign_key_constraint_on: study_request_id
insert_permissions:
  - role: user
    permission:
      check:
        study_request:
          site_id:
            _eq: X-Hasura-Site-Id
      columns:
        - actioned_at
        - note
        - procedure_ids
        - provisional_procedure_date
        - rejection_reason
        - status
        - study_request_id
    comment: ""
select_permissions:
  - role: user
    permission:
      columns:
        - procedure_ids
        - study_request_id
        - status
        - id
        - note
        - rejection_reason
        - actioned_at
        - provisional_procedure_date
      filter:
        study_request:
          site_id:
            _eq: X-Hasura-Site-Id
    comment: ""
update_permissions:
  - role: user
    permission:
      columns:
        - actioned_at
        - note
        - procedure_ids
        - provisional_procedure_date
        - rejection_reason
        - status
      filter:
        study_request:
          site_id:
            _eq: X-Hasura-Site-Id
      check: {}
    comment: ""
