import logging
import uuid
from sqlalchemy import text
from app import db
from app.interface.models import InterfaceQueue
from app.pdf_convert.models import PasPt
from app.message_queue.models import OutgoingMessageQueue


def process_interface_queue():
    """
    Job to read messages from the interface_queue table, get report content,
    and create a message in the outgoing_message_queue.
    """
    logging.info("Starting process_interface_queue.")

    for _ in range(100):  # Process up to 100 messages
        # Lock and retrieve the next available message.
        item = db.session.query(InterfaceQueue).filter(
            InterfaceQueue.processed < 0
        ).order_by(InterfaceQueue.event_date_time.asc()).with_for_update(skip_locked=True).first()

        if not item:
            break

        logging.info(f"Processing interface_queue item {item.interface_queue_id}")

        try:
            # Get site_id from patient
            patient = db.session.query(PasPt).filter_by(patientid=item.patient_id).first()
            if not patient or not hasattr(patient, 'site_id') or not patient.site_id:
                logging.error(f"Could not find patient or site_id for interface_queue_id {item.interface_queue_id}")
                item.processed = 3  # Configuration Error
                item.failure_reason = "Could not find patient or site_id"
                db.session.commit()
                continue

            interface_result = db.session.execute(text("SELECT interface_name FROM interfaces WHERE interface_id = :id"), {'id': item.interface_id}).first()
            if not interface_result:
                logging.error(f"Could not find interface for interface_queue_id {item.interface_queue_id} with interface_id {item.interface_id}")
                item.processed = 3  # Configuration Error
                item.failure_reason = f"Could not find interface with id {item.interface_id}"
                db.session.commit()
                continue

            queue_name = interface_result[0]

            payload = item.to_dict()
            payload['report_content'] = item.report.report_content

            # Create a message for the outgoing queue
            outgoing_message = OutgoingMessageQueue(
                site_id=patient.site_id,
                message_id=str(uuid.uuid4()),
                queue_name=queue_name,
                payload=payload,
                status='pending'
            )
            db.session.add(outgoing_message)

            # Mark as processed
            item.processed = 1  # Succeeded
            db.session.commit()
            logging.info(f"Successfully processed interface_queue item {item.interface_queue_id} and created outgoing message.")

        except Exception as e:
            db.session.rollback()
            logging.exception(f"Failed to process interface_queue item {item.interface_queue_id}. Error: {e}")
            try:
                # After rollback, item is detached. We need to update it in a new transaction.
                db.session.query(InterfaceQueue).filter_by(interface_queue_id=item.interface_queue_id).update({'processed': 2, 'failure_reason': str(e)})  # Failed
                db.session.commit()
            except Exception as e2:
                logging.error(f"Could not mark interface_queue item {item.interface_queue_id} as failed. Error: {e2}")
                db.session.rollback()

    logging.info("Finished process_interface_queue job.")
