import {gql} from '@apollo/client';

import {graphql} from '@/graphql';

// export const getAllStudyRequest = graphql(`
//   query GetAllStudyRequests($unit_id: bigint) {
//     study_request_item(where: {procedure: {unit_id: {_eq: $unit_id}}}) {
//       id
//       rejection_reason
//       procedure {
//         name
//         unit_id
//       }
//       note
//       provisional_procedure_date
//       status
//       actioned_at
//       study_request {
//         id
//         pdf_file_url
//         type
//         requesting_doctor_id
//         report_cc_id
//         pas_pt {
//           patientid
//           dob
//           pas_pt_names {
//             firstname
//             surname
//           }
//         }
//         site_id
//         urgency
//         appointment_note
//         date_recieved
//         date_created
//       }
//     }
//   }
// `);


export const getAllStudyRequest = graphql(`
  query GetAllStudyRequests {
    study_request_item {
      id
      rejection_reason
      procedure_ids
      note
      provisional_procedure_date
      status
      actioned_at
      study_request {
        id
        pdf_file_url
        type
        requesting_doctor_id
        report_cc_id
        pas_pt {
          patientid
          dob
          pas_pt_names {
            firstname
            surname
          }
        }
        site_id
        urgency
        appointment_note
        date_recieved
        date_created
      }
    }
  }
`);


export const getStudyRequest = graphql(`
  query GetStudyRequest($id: Int!) {
    study_request_by_pk(id: $id) {
      id
      pdf_file_url
      type
      requesting_doctor_id
      report_cc_id
      patient_id
      site_id
      urgency
      appointment_note
      date_recieved
      date_created

      pas_pt {
        ur
        dob
        gender_code
        pas_pt_names {
          firstname
          surname
          title
          middlename
        }
      }
      requesting_doctor {
        id
        forename
        surname
        title
        provider_number
      }

      cc_doctor {
        id
        forename
        surname
        title
        provider_number
      }

      site {
        id
        name
      }

      study_request_items {
        id
        study_request_id
        note
        provisional_procedure_date
        status
        procedure_ids
      }
    }
  }
`);

export const createStudyRequest = graphql(`
  mutation CreateStudyRequest(
    $study_request: study_request_insert_input!
  ) {
    insert_study_request_one(
      object: $study_request
    ) {
      id
      pdf_file_url
      type
      requesting_doctor_id
      report_cc_id
      patient_id
      site_id
      urgency
      appointment_note
      date_recieved
      date_created
    }
  }
`);

export const mutateStudyRequestItem = (fieldName: string, value: string | number | null) => {
  return gql`
    mutation UpdateStudyRequestItem($id: Int!, $value: String) {
      update_study_request_item(where: {id: {_eq: $id}}, _set: {${fieldName}: "${value}"}) {
        affected_rows
        returning {
          id

        }
      }
    }
  `;
};

export const mutateStudyRequestField = (fieldName: string, value: string | number | null) => {
  return gql`
    mutation UpdateStudyRequestField($id: Int!, $value: String) {
      update_study_request(where: {id: {_eq: $id}}, _set: {${fieldName}: "${value}"}) {
        affected_rows
        returning {
          id

        }
      }
    }
  `;
};

export const rejectStudyRequestItem = graphql(`
  mutation RejectStudyRequestItem($id: Int!, $rejection_reason: String, $status: String!) {
    update_study_request_item(
      where: {id: {_eq: $id}}
      _set: {rejection_reason: $rejection_reason, status: $status}
    ) {
      affected_rows
      returning {
        id
      }
    }
  }
`);

export const updateStudyRequestItemDetails = graphql(`
  mutation UpdateStudyRequestItemDetails(
    $id: Int!
    $procedure_ids: [Int!]
    $provisional_procedure_date: timestamp
    $note: String
  ) {
    update_study_request_item(
      where: {id: {_eq: $id}}
      _set: {
        procedure_ids: $procedure_ids
        provisional_procedure_date: $provisional_procedure_date
        note: $note
      }
    ) {
      affected_rows
      returning {
        id
        procedure_ids
        provisional_procedure_date
        note
        status
      }
    }
  }
`);

export const getStudyRequestItem = graphql(`
  query GetStudyRequestItem($id: Int!) {
    study_request_item_by_pk(id: $id) {
      id
      study_request_id
      procedure_id
      rejection_reason
      note
      provisional_procedure_date
      status
      actioned_at

      procedure {
        id
        name
        seq_number
      }

      study_request {
        id
        pdf_file_url
        type
        requesting_doctor_id
        report_cc_id
        patient_id
        site_id
        urgency
        appointment_note
        date_recieved
        date_created

        pas_pt {
          patientid
          ur
          dob
          gender_code
          pas_pt_names {
            firstname
            surname
            title
            middlename
          }
        }

        requesting_doctor {
          id
          forename
          surname
          title
          provider_number
        }

        cc_doctor {
          id
          forename
          surname
          title
          provider_number
        }

        site {
          id
          name
        }
      }
    }
  }
`);
