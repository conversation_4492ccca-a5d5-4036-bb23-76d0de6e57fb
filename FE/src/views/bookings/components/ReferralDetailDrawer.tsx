import {ComponentProps, useEffect, useRef, useState} from 'react';
import {Di<PERSON>, <PERSON>ing, Modal, Button as RACButton} from 'react-aria-components';
import {ListBox, ListBoxItem, Popover, Select, SelectValue} from 'react-aria-components';
import {FormProvider, useForm} from 'react-hook-form';
import {useLocation, useNavigate, useParams, useSearchParams} from 'react-router';
import {useAsyncList} from 'react-stately';

import {useApolloClient, useMutation, useQuery} from '@apollo/client';
import {format} from 'date-fns';
import {
  CheckIcon,
  ChevronDown,
  ExternalLinkIcon,
  FileIcon,
  FilesIcon,
  Plus,
  Undo2Icon,
  UserIcon,
  VenusAndMarsIcon,
  X,
} from 'lucide-react';

import Calendar2Icon from '@/assets/iconly/Calendar2.svg?react';
import HashTagIcon from '@/assets/iconly/HashTag.svg?react';
import EditProcedureForStudyItem from '@/components/EditProcedureForStudyItem.tsx';
import Editable from '@/components/Editable.tsx';
import {CheckmarkIconly} from '@/components/icons/CheckmarkIconly.tsx';
import {CloseRemoveIconly} from '@/components/icons/CloseRemoveIconly.tsx';
import {EditSquareIconly} from '@/components/icons/EditSquareIconly.tsx';
import {useDialogState} from '@/components/modal-state-provider';
import {Label} from '@/components/ui/Field.tsx';
import {Button} from '@/components/ui/button';
import {Form} from '@/components/ui/form';
import {WatchState} from '@/components/ui/form/WatchState.tsx';
import {
  createStudyRequestItem,
  getAllStudyRequest,
  getStudyRequest,
  mutateStudyRequestField,
  mutateStudyRequestItem,
  updateStudyRequestItemDetails,
} from '@/graphql/bookings.ts';
import {getProceduresList, getUnitsList} from '@/graphql/lists.ts';
import {Patient} from '@/store/patient.ts';

import {DoctorType, doctorListLoader, formatDoctorName, patientsListLoader} from './AddReferralDrawer';

export function ReferralDetailDrawer(props: ComponentProps<typeof Modal>) {
  const params = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const form = useForm();
  const [searchParams, setSearchParams] = useSearchParams();
  const [, setRejectOpen] = useDialogState('reject-booking-dialogue');
  const client = useApolloClient();
  const path = location.pathname;
  const regex = /^\/bookings\/referrals\/(\d+)$/;

  const match = path.match(regex);

  const fileInputRef = useRef(null);
  const [error, setError] = useState<null | string>(null);
  const [editingItemId, setEditingItemId] = useState<number | null>(null);
  const [newItems, setNewItems] = useState<
    Array<{
      id: string;
      procedure_ids: number[];
      provisional_procedure_date: string;
      note: string;
    }>
  >([]);
  const [selectedUnit, setSelectedUnit] = useState<number | undefined>(undefined);

  const patientsList = useAsyncList<Patient>({
    getKey: (p) => p.patientid,
    load: patientsListLoader,
  });

  const doctorsList = useAsyncList<DoctorType>({
    getKey: (p) => p.id,
    load: doctorListLoader,
  });

  const doctorsCCList = useAsyncList<DoctorType>({
    getKey: (p) => p.id,
    load: doctorListLoader,
  });

  const handleClick = () => {
    (fileInputRef?.current as any)?.click();
  };

  const handleFileInput = (event: any) => {
    const files = event.target.files;
    handleFiles(files);
  };

  const handleFiles = (files: any) => {
    if (files.length > 0) {
      const file = files[0];
      if (file.type === 'application/pdf' && file.size <= 20 * 1024 * 1024) {
        console.log('Valid PDF file:', file.name);
        form.setValue('pdf_file_url', file);
      } else {
        setError(
          file.type !== 'application/pdf' ? 'Only PDF files are allowed.' : 'File size exceeds 20 MB.'
        );
      }
    }
  };

  const {data: studyRequest} = useQuery(getStudyRequest, {
    variables: {id: parseInt(params?.studyRequestId ?? '')},
  });

  const [updateStudyRequestItem] = useMutation(mutateStudyRequestItem('status', 'accepted'));
  const [updateStudyRequestItemDetailsMutation] = useMutation(updateStudyRequestItemDetails);
  const [createStudyRequestItemMutation] = useMutation(createStudyRequestItem);

  const data = studyRequest?.study_request_by_pk;
  const {data: procedures} = useQuery(getProceduresList);
  const {data: unitsData} = useQuery(getUnitsList);

  // Initialize selected unit
  useEffect(() => {
    if (unitsData?.list_units?.length && !selectedUnit) {
      setSelectedUnit(unitsData.list_units[0].id);
    }
  }, [unitsData, selectedUnit]);

  // Filter procedures by selected unit
  const filteredProcedures =
    procedures?.procedure?.filter((proc) => !selectedUnit || proc.unit_id === selectedUnit) || [];

  const procedureItems = filteredProcedures.map((p) => ({
    id: p.id?.toString(),
    value: `${p.seq_number}. ${p.name}`,
    unit_id: p.unit_id as number,
    name: p.name,
    seq_number: p.seq_number as number,
  }));

  const handleAccept = (itemId: number) => {
    updateStudyRequestItem({
      variables: {id: itemId, value: 'accepted'},
      refetchQueries: [{query: getAllStudyRequest}, {query: getStudyRequest}],
    });
  };

  const handleReject = (itemId: number) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('reject', itemId.toString());
    setSearchParams(newSearchParams, {replace: true});
    setRejectOpen(true);
  };

  const handleEditProcedure = (itemId: number | string) => {
    setEditingItemId(itemId as any);
  };

  const handleSaveProcedureChanges = async (data: any) => {
    if (!editingItemId) return;

    try {
      await updateStudyRequestItemDetailsMutation({
        variables: {
          id: editingItemId,
          procedure_ids: data.procedure_ids || [],
          provisional_procedure_date: data.provisional_procedure_date || null,
          note: data.note || null,
        },
        refetchQueries: [{query: getStudyRequest, variables: {id: parseInt(params?.studyRequestId ?? '')}}],
      });
      setEditingItemId(null);
    } catch (error) {
      console.error('Error updating procedure:', error);
    }
  };

  const handleCancelEdit = () => {
    setEditingItemId(null);
  };

  const handleAddNewProcedure = () => {
    const newItemId = `new-${Date.now()}`;
    const newItem = {
      id: newItemId,
      procedure_ids: [],
      provisional_procedure_date: '',
      note: '',
    };
    setNewItems((prev) => [...prev, newItem]);
    setEditingItemId(newItemId as any);
  };

  const handleSaveNewItem = async (formData: any, itemId: string) => {
    if (!data?.id) return;

    try {
      await createStudyRequestItemMutation({
        variables: {
          study_request_id: data.id,
          procedure_ids: formData.procedure_ids || [],
          provisional_procedure_date: formData.provisional_procedure_date || null,
          note: formData.note || null,
        },
        refetchQueries: [{query: getStudyRequest, variables: {id: parseInt(params?.studyRequestId ?? '')}}],
      });
      // Remove from newItems after successful save
      setNewItems((prev) => prev.filter((item) => item.id !== itemId));
      setEditingItemId(null);
    } catch (error) {
      console.error('Error creating new procedure item:', error);
    }
  };

  const handleCancelNewItem = (itemId: string) => {
    setNewItems((prev) => prev.filter((item) => item.id !== itemId));
    setEditingItemId(null);
  };

  return (
    <Modal
      isDismissable={props.isDismissable ?? true}
      isOpen={!!match}
      onOpenChange={() => {
        navigate(-1);
      }}
      className="react-aria-Drawer"
      {...props}
    >
      <Dialog className="react-aria-Dialog flex min-h-full flex-col gap-y-4">
        <div className="no-inset shrink-0 border-b border-neutral-200">
          <Heading slot="title">Referral Detail</Heading>
          <RACButton slot="close">
            <X />
          </RACButton>
        </div>

        <FormProvider {...form}>
          <Form className="flex flex-1 flex-col">
            <div className="flex-1">
              <WatchState name="pdf_file_url">
                {(file) => (
                  <>
                    {file && (
                      <div className="mb-5 flex items-center justify-between rounded-sm border border-dashed border-neutral-300 bg-neutral-100 p-3">
                        <div className="flex items-center gap-x-1.5">
                          <FilesIcon className="text-brand-700 h-4.5 w-4.5" />
                          <div className="text-sm text-neutral-800">{file?.name ?? 'abc.pdf'}</div>
                        </div>
                        <div className="flex items-center gap-x-2">
                          <button
                            className="flex cursor-pointer items-center gap-x-1 text-xs font-semibold text-[#EF4444]"
                            onClick={() => form.setValue('pdf_file_url', null)}
                            type="button"
                          >
                            <X className="h-3 w-3" />
                            Remove
                          </button>
                          <button
                            className="text-brand-500 flex cursor-pointer items-center gap-x-1 rounded-md border border-neutral-300 px-1.5 py-0.5 text-xs font-medium"
                            onClick={handleClick}
                            type="button"
                          >
                            <Undo2Icon className="h-3 w-3" />
                            Reupload
                          </button>
                        </div>
                        <input
                          type="file"
                          accept="application/pdf"
                          ref={fileInputRef}
                          className="hidden"
                          onChange={handleFileInput}
                        />
                      </div>
                    )}
                  </>
                )}
              </WatchState>

              {error && <p className="-mt-3 mb-5 text-xs text-red-500">{error ?? 'Error uploading file.'}</p>}

              <div className="space-y-0.5">
                <Editable
                  defaultValue={
                    data?.pas_pt?.pas_pt_names?.[0]?.firstname +
                    ' ' +
                    data?.pas_pt?.pas_pt_names?.[0]?.surname
                  }
                  onSubmit={async (value) => {
                    await client.mutate({
                      mutation: mutateStudyRequestField('patient_id', value),
                      refetchQueries: [getStudyRequest],
                      variables: {id: data?.id},
                    });
                  }}
                >
                  {({setIsEditing, previewValue, setPreviewValue}) => (
                    <>
                      <Editable.Trigger>
                        <div className="group -mx-2 flex h-7 w-full items-center gap-x-2 rounded-md px-2 hover:bg-neutral-100">
                          <div className="flex shrink-0 items-center gap-x-2">
                            <UserIcon className="size-4.5 text-neutral-500" />
                            <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">
                              Full Name:
                            </Label>
                          </div>

                          <div className="grow truncate text-left text-xs text-neutral-900">
                            {previewValue as string}
                          </div>

                          <button
                            className="hidden cursor-pointer group-hover:flex hover:flex"
                            onClick={() => setIsEditing(true)}
                          >
                            <EditSquareIconly className="text-brand-500 h-4.5 w-4.5" />
                          </button>
                        </div>
                      </Editable.Trigger>
                      <Editable.Content className="mx-1 flex w-full items-center gap-x-4">
                        <Editable.Field
                          type="combobox"
                          items={patientsList.items}
                          onInputChange={(value) => {
                            setPreviewValue('');
                            patientsList.setFilterText(value);
                          }}
                          inputValue={(previewValue as string) || patientsList.filterText}
                          itemIdKey="patientid"
                          itemValueKey="fullName"
                          placeholder="Search Patients"
                        />
                        <div className="flex items-center gap-x-2">
                          <Button
                            variant="outlined"
                            className="text-brand-500 focus-visible:outline-brand-500 cursor-pointer rounded-sm border p-1 focus-visible:outline-1"
                            aria-label="Cancel"
                            slot="cancel"
                          >
                            <CloseRemoveIconly className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outlined"
                            className="text-brand-500 focus-visible:outline-brand-500 cursor-pointer rounded-sm border p-1 focus-visible:outline-1"
                            aria-label="Submit"
                            slot="submit"
                          >
                            <CheckmarkIconly className="h-4 w-4" />
                          </Button>
                        </div>
                      </Editable.Content>
                    </>
                  )}
                </Editable>

                <div className="grid grid-cols-3">
                  <div className="group -mx-2 flex h-7 w-full items-center gap-x-2 rounded-md px-2">
                    <div className="flex shrink-0 items-center gap-x-2">
                      <HashTagIcon className="size-4.5 text-neutral-500" />
                      <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">MRN:</Label>
                    </div>

                    <div className="grow truncate text-left text-xs text-neutral-900">{data?.pas_pt?.ur}</div>
                  </div>
                  <div className="group -mx-2 flex h-7 w-full items-center gap-x-2 rounded-md px-2">
                    <div className="flex shrink-0 items-center gap-x-2">
                      <Calendar2Icon className="size-4.5 text-neutral-500" />
                      <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">DOB:</Label>
                    </div>

                    <div className="grow truncate text-left text-xs text-neutral-900">
                      {data?.pas_pt?.dob}
                    </div>
                  </div>
                  <div className="group -mx-2 flex h-7 w-full items-center gap-x-2 rounded-md px-2">
                    <div className="flex shrink-0 items-center gap-x-2">
                      <VenusAndMarsIcon className="size-4.5 text-neutral-500" />
                      <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">Gender:</Label>
                    </div>

                    <div className="grow truncate text-left text-xs text-neutral-900">
                      {data?.pas_pt?.gender_code}
                    </div>
                  </div>
                </div>

                <div className="group -mx-2 flex h-8 w-full items-center gap-x-2 rounded-md px-2 hover:bg-neutral-100">
                  <div className="flex shrink-0 items-center gap-x-2">
                    <FileIcon className="size-4.5 text-neutral-500" />
                    <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">Source:</Label>
                  </div>

                  <div className="grow truncate text-left text-xs text-neutral-900 capitalize">
                    {data?.type}
                  </div>
                </div>

                <div className="group -mx-2 flex h-8 w-full items-center gap-x-2 rounded-md px-2 hover:bg-neutral-100">
                  <div className="flex shrink-0 items-center gap-x-2">
                    <Calendar2Icon className="size-4.5 text-neutral-500" />
                    <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">
                      Referral Date:
                    </Label>
                  </div>

                  <div className="grow truncate text-left text-xs text-neutral-900">
                    {data?.date_recieved
                      ? format(new Date((data?.date_recieved as any) ?? null), 'dd/MM/yyyy')
                      : ''}
                  </div>
                </div>
              </div>

              <div className="text-brand-500 mt-4 flex items-center gap-x-1">
                <a
                  target="_blank"
                  href={`/patients/${data?.patient_id}`}
                  className="text-brand-500 cursor-point text-xs font-semibold capitalize underline"
                >
                  Patient Profile
                </a>
                <ExternalLinkIcon className="h-3 w-3" />
              </div>

              <hr className="mt-6 mb-4 text-neutral-100" />

              <div className="my-4 text-xs leading-snug font-bold tracking-wide text-neutral-800 uppercase">
                Doctor Details
              </div>
              <div className="space-y-0.5">
                <Editable
                  defaultValue={data?.requesting_doctor?.forename + ' ' + data?.requesting_doctor?.surname}
                  // defaultValue={data?.requesting_doctor?.id}
                  onSubmit={async (value) => {
                    await client.mutate({
                      mutation: mutateStudyRequestField('requesting_doctor_id', value),
                      refetchQueries: [getStudyRequest],
                      variables: {id: data?.id},
                    });
                  }}
                >
                  {({setIsEditing, previewValue, setPreviewValue}) => (
                    <>
                      <Editable.Trigger>
                        <div className="group -mx-2 flex h-7 w-full items-center gap-x-2 rounded-md px-2 hover:bg-neutral-100">
                          <div className="flex shrink-0 items-center gap-x-2">
                            <UserIcon className="size-4.5 text-neutral-500" />
                            <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">
                              Requesting Doctor:
                            </Label>
                          </div>

                          <div className="grow truncate text-left text-xs text-neutral-900 capitalize">
                            {previewValue as string}
                          </div>

                          <button
                            className="hidden cursor-pointer group-hover:flex hover:flex"
                            onClick={() => setIsEditing(true)}
                          >
                            <EditSquareIconly className="text-brand-500 h-4.5 w-4.5" />
                          </button>
                        </div>
                      </Editable.Trigger>
                      <Editable.Content className="mx-1 flex w-full items-center gap-x-4">
                        <Editable.Field
                          type="combobox"
                          items={doctorsList.items}
                          onInputChange={(value) => {
                            setPreviewValue('');
                            doctorsList.setFilterText(value);
                          }}
                          inputValue={(previewValue as string) || doctorsList.filterText}
                          itemIdKey="id"
                          itemValueKey="forename"
                          itemValueFormatter={formatDoctorName}
                          placeholder="Search Doctors"
                        />
                        <div className="flex items-center gap-x-2">
                          <Button
                            variant="outlined"
                            className="text-brand-500 focus-visible:outline-brand-500 cursor-pointer rounded-sm border p-1 focus-visible:outline-1"
                            aria-label="Cancel"
                            slot="cancel"
                          >
                            <CloseRemoveIconly className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outlined"
                            className="text-brand-500 focus-visible:outline-brand-500 cursor-pointer rounded-sm border p-1 focus-visible:outline-1"
                            aria-label="Submit"
                            slot="submit"
                          >
                            <CheckmarkIconly className="h-4 w-4" />
                          </Button>
                        </div>
                      </Editable.Content>
                    </>
                  )}
                </Editable>

                <div className="group -mx-2 flex h-8 w-full items-center gap-x-2 rounded-md px-2 hover:bg-neutral-100">
                  <div className="flex shrink-0 items-center gap-x-2">
                    <HashTagIcon className="size-4.5 text-neutral-500" />
                    <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">
                      Requesting Doctor Provider #:
                    </Label>
                  </div>

                  <div className="grow truncate text-left text-xs text-neutral-900 capitalize">
                    {data?.requesting_doctor?.provider_number}
                  </div>
                </div>

                <Editable
                  defaultValue={
                    data?.cc_doctor?.forename +
                    ' ' +
                    data?.cc_doctor?.surname +
                    ` (${data?.cc_doctor?.provider_number})`
                  }
                  onSubmit={async (value) => {
                    await client.mutate({
                      mutation: mutateStudyRequestField('report_cc_id', value),
                      refetchQueries: [getStudyRequest],
                      variables: {id: data?.id},
                    });
                  }}
                >
                  {({setIsEditing, previewValue, setPreviewValue}) => (
                    <>
                      <Editable.Trigger>
                        <div className="group -mx-2 flex h-7 w-full items-center gap-x-2 rounded-md px-2 hover:bg-neutral-100">
                          <div className="flex shrink-0 items-center gap-x-2">
                            <UserIcon className="size-4.5 text-neutral-500" />
                            <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">
                              CC Doctor:
                            </Label>
                          </div>

                          <div className="grow truncate text-left text-xs text-neutral-900 capitalize">
                            {previewValue as string}
                          </div>

                          <button
                            className="hidden cursor-pointer group-hover:flex hover:flex"
                            onClick={() => setIsEditing(true)}
                          >
                            <EditSquareIconly className="text-brand-500 h-4.5 w-4.5" />
                          </button>
                        </div>
                      </Editable.Trigger>
                      <Editable.Content className="mx-1 flex w-full items-center gap-x-4">
                        <Editable.Field
                          type="combobox"
                          items={doctorsCCList.items}
                          onInputChange={(value) => {
                            setPreviewValue('');
                            doctorsCCList.setFilterText(value);
                          }}
                          inputValue={(previewValue as string) || doctorsCCList.filterText}
                          itemIdKey="id"
                          itemValueKey="fullName"
                          itemValueFormatter={(item) => `${formatDoctorName(item)} (${item.provider_number})`}
                          placeholder="Search CC Doctors"
                        />
                        <div className="flex items-center gap-x-2">
                          <Button
                            variant="outlined"
                            className="text-brand-500 focus-visible:outline-brand-500 cursor-pointer rounded-sm border p-1 focus-visible:outline-1"
                            aria-label="Cancel"
                            slot="cancel"
                          >
                            <CloseRemoveIconly className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outlined"
                            className="text-brand-500 focus-visible:outline-brand-500 cursor-pointer rounded-sm border p-1 focus-visible:outline-1"
                            aria-label="Submit"
                            slot="submit"
                          >
                            <CheckmarkIconly className="h-4 w-4" />
                          </Button>
                        </div>
                      </Editable.Content>
                    </>
                  )}
                </Editable>
              </div>

              <hr className="mt-6 mb-4 text-neutral-100" />

              <div className="my-4 text-xs leading-snug font-bold tracking-wide text-neutral-800 uppercase">
                Procedure Details
              </div>

              <div className="mb-4">
                <Label className="mb-2 block text-xs text-neutral-700">Unit</Label>
                <Select
                  aria-label="Units"
                  selectedKey={selectedUnit}
                  onSelectionChange={(s: any) => {
                    setSelectedUnit(s);
                  }}
                >
                  <RACButton className="react-aria-Button h-9 w-full rounded-sm">
                    <SelectValue className="react-aria-SelectValue text-xs text-neutral-900 capitalize" />
                    <ChevronDown />
                  </RACButton>
                  <Popover>
                    <ListBox items={unitsData?.list_units}>
                      {(unit) => (
                        <ListBoxItem
                          className="react-aria-ListBoxItem text-sm"
                          id={unit.id}
                          textValue={String(unit.id)}
                        >
                          {unit.description}
                        </ListBoxItem>
                      )}
                    </ListBox>
                  </Popover>
                </Select>
              </div>

              {(data?.study_request_items && data.study_request_items.length > 0) || newItems.length > 0 ? (
                [...(data?.study_request_items || []), ...newItems].map((item) => (
                  <div
                    key={item.id}
                    className="border-brand-100 my-4 space-y-0.5 rounded border p-4"
                  >
                    {editingItemId === item.id ? (
                      <EditProcedureForStudyItem
                        procedures={procedureItems}
                        studyRequestItem={{
                          id: item.id,
                          procedure_ids: item.procedure_ids || [],
                          provisional_procedure_date: item?.provisional_procedure_date
                            ? item.provisional_procedure_date.toString()
                            : '',
                          note: item.note || '',
                        }}
                        onSubmit={(formData) => {
                          if (typeof item.id === 'string' && item.id.startsWith('new-')) {
                            handleSaveNewItem(formData, item.id);
                          } else {
                            handleSaveProcedureChanges(formData);
                          }
                        }}
                        onCancel={() => {
                          if (typeof item.id === 'string' && item.id.startsWith('new-')) {
                            handleCancelNewItem(item.id);
                          } else {
                            handleCancelEdit();
                          }
                        }}
                      />
                    ) : (
                      <>
                        <div className="group -mx-2 flex h-8 w-full items-center gap-x-2 rounded-md px-2 hover:bg-neutral-100">
                          <div className="flex shrink-0 items-center gap-x-2">
                            <UserIcon className="size-4.5 text-neutral-500" />
                            <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">
                              Procedure:
                            </Label>
                          </div>

                          <div className="grow truncate text-left text-xs text-neutral-900">
                            {item.procedure_ids && item.procedure_ids.length > 0 ? (
                              item.procedure_ids.map((procId, index) => {
                                const proc = procedures?.procedure?.find((p) => p.id === procId);
                                return proc ? (
                                  <span key={procId}>
                                    {proc.seq_number as number}. {proc.name}
                                    {index < item.procedure_ids!.length - 1 && ', '}
                                  </span>
                                ) : null;
                              })
                            ) : (
                              <span className="text-neutral-500">
                                {typeof item.id === 'string' && item.id.startsWith('new-')
                                  ? 'New procedure - click edit to configure'
                                  : 'No procedures selected'}
                              </span>
                            )}
                          </div>

                          <button
                            className="text-brand-500 cursor-pointer text-xs font-semibold"
                            onClick={() => handleEditProcedure(item.id)}
                          >
                            edit
                          </button>
                        </div>

                        <div className="group -mx-2 flex h-8 w-full items-center gap-x-2 rounded-md px-2 hover:bg-neutral-100">
                          <div className="flex shrink-0 items-center gap-x-2">
                            <UserIcon className="size-4.5 text-neutral-500" />
                            <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">
                              Provisional Date:
                            </Label>
                          </div>

                          <div className="grow truncate text-left text-xs text-neutral-900">
                            {item.provisional_procedure_date
                              ? format(new Date(item.provisional_procedure_date as any), 'dd MMM, yyyy')
                              : 'Not specified'}
                          </div>
                        </div>

                        {!(typeof item.id === 'string' && item.id.startsWith('new-')) && (
                          <div className="group -mx-2 flex h-8 w-full items-center gap-x-2 rounded-md px-2 hover:bg-neutral-100">
                            <div className="flex shrink-0 items-center gap-x-2">
                              <UserIcon className="size-4.5 text-neutral-500" />
                              <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">
                                Status:
                              </Label>
                            </div>

                            <div className="grow truncate text-left text-xs text-neutral-900 capitalize">
                              {(item as any).status}
                            </div>
                          </div>
                        )}

                        {item.note && (
                          <div className="group -mx-2 flex flex-col gap-y-2 rounded-md px-2 py-1 hover:bg-neutral-100">
                            <div className="flex shrink-0 items-center gap-x-2">
                              <UserIcon className="size-4.5 text-neutral-500" />
                              <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">
                                Test/Clinical Notes:
                              </Label>
                            </div>

                            <div className="grow text-left text-xs text-neutral-900">{item.note}</div>
                          </div>
                        )}

                        {!(typeof item.id === 'string' && item.id.startsWith('new-')) && (
                          <>
                            {(item as any).status !== 'accepted' && (item as any).status !== 'rejected' && (
                              <div className="mt-4 flex items-center justify-end gap-x-2">
                                <Button
                                  variant="outlined"
                                  color="danger"
                                  size="small"
                                  className="h-7 rounded-md"
                                  onPress={() => handleReject(item.id as number)}
                                >
                                  <X data-slot="icon" />
                                  Reject
                                </Button>
                                <Button
                                  variant="outlined"
                                  color="success"
                                  size="small"
                                  className="h-7 rounded-md"
                                  onPress={() => handleAccept(item.id as number)}
                                >
                                  <CheckIcon data-slot="icon" />
                                  Accept
                                </Button>
                              </div>
                            )}

                            {(item as any).status === 'accepted' && (
                              <div className="mt-4 flex items-center justify-end">
                                <div className="flex items-center gap-x-1 text-xs font-semibold text-[#16A34A]">
                                  <CheckIcon className="h-4 w-4" />
                                  Accepted
                                </div>
                              </div>
                            )}

                            {(item as any).status === 'rejected' && (
                              <div className="mt-4 flex items-center justify-end">
                                <div className="flex items-center gap-x-1 text-xs font-semibold text-[#EF4444]">
                                  <X className="h-4 w-4" />
                                  Rejected
                                </div>
                              </div>
                            )}
                          </>
                        )}
                      </>
                    )}
                  </div>
                ))
              ) : (
                <div className="my-4 text-center text-sm text-neutral-500">
                  No procedures found for this referral.
                </div>
              )}

              <div className="my-4">
                <Button
                  variant="plain"
                  color="brand"
                  size="small"
                  onPress={handleAddNewProcedure}
                >
                  <Plus data-slot="icon" />
                  Add Procedure
                </Button>
              </div>
            </div>
          </Form>
        </FormProvider>
      </Dialog>
    </Modal>
  );
}
