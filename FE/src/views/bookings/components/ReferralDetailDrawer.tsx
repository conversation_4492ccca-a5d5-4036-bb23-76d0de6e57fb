import {ComponentProps, useRef, useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON>, Mo<PERSON>, Button as RACButton} from 'react-aria-components';
import {useForm, FormProvider} from 'react-hook-form';
import {useLocation, useNavigate, useParams, useSearchParams} from 'react-router';
import {useAsyncList} from 'react-stately';

import {useApolloClient, useMutation, useQuery} from '@apollo/client';
import {format} from 'date-fns';
import {
  CheckIcon,
  ExternalLinkIcon,
  FileIcon,
  FilesIcon,
  Undo2Icon,
  UserIcon,
  VenusAndMarsIcon,
  X,
} from 'lucide-react';

import Calendar2Icon from '@/assets/iconly/Calendar2.svg?react';
import HashTagIcon from '@/assets/iconly/HashTag.svg?react';
import Editable from '@/components/Editable.tsx';
import {CheckmarkIconly} from '@/components/icons/CheckmarkIconly.tsx';
import {CloseRemoveIconly} from '@/components/icons/CloseRemoveIconly.tsx';
import {EditSquareIconly} from '@/components/icons/EditSquareIconly.tsx';
import {useDialogState} from '@/components/modal-state-provider';
import {Label} from '@/components/ui/Field.tsx';
import {Button} from '@/components/ui/button';

import {Form} from '@/components/ui/form';
import {WatchState} from '@/components/ui/form/WatchState.tsx';
import {
  getAllStudyRequest,
  getStudyRequest,
  mutateStudyRequestField,
  mutateStudyRequestItem,
  updateStudyRequestItemDetails,
} from '@/graphql/bookings.ts';
import {getProceduresList} from '@/graphql/lists.ts';
import {Patient} from '@/store/patient.ts';
import EditProcedureForStudyItem from '@/components/EditProcedureForStudyItem.tsx';

import {DoctorType, doctorListLoader, formatDoctorName, patientsListLoader} from './AddReferralDrawer';

export function ReferralDetailDrawer(props: ComponentProps<typeof Modal>) {
  const params = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const form = useForm();
  const [searchParams, setSearchParams] = useSearchParams();
  const [, setRejectOpen] = useDialogState('reject-booking-dialogue');
  const client = useApolloClient();
  const path = location.pathname;
  const regex = /^\/bookings\/referrals\/(\d+)$/;

  const match = path.match(regex);

  const fileInputRef = useRef(null);
  const [error, setError] = useState<null | string>(null);
  const [editingItemId, setEditingItemId] = useState<number | null>(null);

  const patientsList = useAsyncList<Patient>({
    getKey: (p) => p.patientid,
    load: patientsListLoader,
  });

  const doctorsList = useAsyncList<DoctorType>({
    getKey: (p) => p.id,
    load: doctorListLoader,
  });

  const doctorsCCList = useAsyncList<DoctorType>({
    getKey: (p) => p.id,
    load: doctorListLoader,
  });

  const handleClick = () => {
    (fileInputRef?.current as any)?.click();
  };

  const handleFileInput = (event: any) => {
    const files = event.target.files;
    handleFiles(files);
  };

  const handleFiles = (files: any) => {
    if (files.length > 0) {
      const file = files[0];
      if (file.type === 'application/pdf' && file.size <= 20 * 1024 * 1024) {
        console.log('Valid PDF file:', file.name);
        form.setValue('pdf_file_url', file);
      } else {
        setError(
          file.type !== 'application/pdf' ? 'Only PDF files are allowed.' : 'File size exceeds 20 MB.'
        );
      }
    }
  };

  const {data: studyRequest} = useQuery(getStudyRequest, {
    variables: {id: parseInt(params?.studyRequestId ?? '')},
  });

  console.log('studyRequest: ', studyRequest);

  const [updateStudyRequestItem] = useMutation(mutateStudyRequestItem('status', 'accepted'));
  const [updateStudyRequestItemDetailsMutation] = useMutation(updateStudyRequestItemDetails);

  const data = studyRequest?.study_request_by_pk;
  console.log('data: ', data);
  const {data: procedures} = useQuery(getProceduresList);

  const procedureItems =
    procedures?.procedure?.map((p) => {
      return {
        id: p.id?.toString(),
        value: `${p.seq_number}. ${p.name}`,
      };
    }) ?? ([] as any);

  const handleAccept = (itemId: number) => {
    updateStudyRequestItem({
      variables: {id: itemId, value: 'accepted'},
      refetchQueries: [{query: getAllStudyRequest}, {query: getStudyRequest}],
    });
  };

  const handleReject = (itemId: number) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('reject', itemId.toString());
    setSearchParams(newSearchParams, {replace: true});
    setRejectOpen(true);
  };

  const handleEditProcedure = (itemId: number) => {
    setEditingItemId(itemId);
  };

  const handleSaveProcedureChanges = async (formData: any) => {
    if (!editingItemId) return;

    const procedureData = formData.procedures?.[0];
    if (!procedureData) return;

    try {
      await updateStudyRequestItemDetailsMutation({
        variables: {
          id: editingItemId,
          procedure_ids: procedureData.procedure_ids || [],
          provisional_procedure_date: procedureData.provisional_date || null,
          note: procedureData.note || null,
        },
        refetchQueries: [{query: getStudyRequest, variables: {id: parseInt(params?.studyRequestId ?? '')}}],
      });
      setEditingItemId(null);
    } catch (error) {
      console.error('Error updating procedure:', error);
    }
  };

  const handleCancelEdit = () => {
    setEditingItemId(null);
  };

  return (
    <Modal
      isDismissable={props.isDismissable ?? true}
      isOpen={!!match}
      onOpenChange={() => {
        navigate(-1);
      }}
      className="react-aria-Drawer"
      {...props}
    >
      <Dialog className="react-aria-Dialog flex min-h-full flex-col gap-y-4">
        <div className="no-inset shrink-0 border-b border-neutral-200">
          <Heading slot="title">Referral Detail</Heading>
          <RACButton slot="close">
            <X />
          </RACButton>
        </div>

        <FormProvider {...form}>
          <Form className="flex flex-1 flex-col">
          <div className="flex-1">
            <WatchState name="pdf_file_url">
              {(file) => (
                <>
                  {file && (
                    <div className="mb-5 flex items-center justify-between rounded-sm border border-dashed border-neutral-300 bg-neutral-100 p-3">
                      <div className="flex items-center gap-x-1.5">
                        <FilesIcon className="text-brand-700 h-4.5 w-4.5" />
                        <div className="text-sm text-neutral-800">{file?.name ?? 'abc.pdf'}</div>
                      </div>
                      <div className="flex items-center gap-x-2">
                        <button
                          className="flex cursor-pointer items-center gap-x-1 text-xs font-semibold text-[#EF4444]"
                          onClick={() => form.setValue('pdf_file_url', null)}
                          type="button"
                        >
                          <X className="h-3 w-3" />
                          Remove
                        </button>
                        <button
                          className="text-brand-500 flex cursor-pointer items-center gap-x-1 rounded-md border border-neutral-300 px-1.5 py-0.5 text-xs font-medium"
                          onClick={handleClick}
                          type="button"
                        >
                          <Undo2Icon className="h-3 w-3" />
                          Reupload
                        </button>
                      </div>
                      <input
                        type="file"
                        accept="application/pdf"
                        ref={fileInputRef}
                        className="hidden"
                        onChange={handleFileInput}
                      />
                    </div>
                  )}
                </>
              )}
            </WatchState>

            {error && <p className="-mt-3 mb-5 text-xs text-red-500">{error ?? 'Error uploading file.'}</p>}

            <div className="space-y-0.5">
              <Editable
                defaultValue={
                  data?.pas_pt?.pas_pt_names?.[0]?.firstname + ' ' + data?.pas_pt?.pas_pt_names?.[0]?.surname
                }
                onSubmit={async (value) => {
                  await client.mutate({
                    mutation: mutateStudyRequestField('patient_id', value),
                    refetchQueries: [getStudyRequest],
                    variables: {id: data?.id},
                  });
                }}
              >
                {({setIsEditing, previewValue, setPreviewValue}) => (
                  <>
                    <Editable.Trigger>
                      <div className="group -mx-2 flex h-7 w-full items-center gap-x-2 rounded-md px-2 hover:bg-neutral-100">
                        <div className="flex shrink-0 items-center gap-x-2">
                          <UserIcon className="size-4.5 text-neutral-500" />
                          <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">
                            Full Name:
                          </Label>
                        </div>

                        <div className="grow truncate text-left text-xs text-neutral-900">
                          {previewValue as string}
                        </div>

                        <button
                          className="hidden cursor-pointer group-hover:flex hover:flex"
                          onClick={() => setIsEditing(true)}
                        >
                          <EditSquareIconly className="text-brand-500 h-4.5 w-4.5" />
                        </button>
                      </div>
                    </Editable.Trigger>
                    <Editable.Content className="mx-1 flex w-full items-center gap-x-4">
                      <Editable.Field
                        type="combobox"
                        items={patientsList.items}
                        onInputChange={(value) => {
                          setPreviewValue('');
                          patientsList.setFilterText(value);
                        }}
                        inputValue={(previewValue as string) || patientsList.filterText}
                        itemIdKey="patientid"
                        itemValueKey="fullName"
                        placeholder="Search Patients"
                      />
                      <div className="flex items-center gap-x-2">
                        <Button
                          variant="outlined"
                          className="text-brand-500 focus-visible:outline-brand-500 cursor-pointer rounded-sm border p-1 focus-visible:outline-1"
                          aria-label="Cancel"
                          slot="cancel"
                        >
                          <CloseRemoveIconly className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outlined"
                          className="text-brand-500 focus-visible:outline-brand-500 cursor-pointer rounded-sm border p-1 focus-visible:outline-1"
                          aria-label="Submit"
                          slot="submit"
                        >
                          <CheckmarkIconly className="h-4 w-4" />
                        </Button>
                      </div>
                    </Editable.Content>
                  </>
                )}
              </Editable>

              <div className="grid grid-cols-3">
                <div className="group -mx-2 flex h-7 w-full items-center gap-x-2 rounded-md px-2">
                  <div className="flex shrink-0 items-center gap-x-2">
                    <HashTagIcon className="size-4.5 text-neutral-500" />
                    <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">MRN:</Label>
                  </div>

                  <div className="grow truncate text-left text-xs text-neutral-900">{data?.pas_pt?.ur}</div>
                </div>
                <div className="group -mx-2 flex h-7 w-full items-center gap-x-2 rounded-md px-2">
                  <div className="flex shrink-0 items-center gap-x-2">
                    <Calendar2Icon className="size-4.5 text-neutral-500" />
                    <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">DOB:</Label>
                  </div>

                  <div className="grow truncate text-left text-xs text-neutral-900">{data?.pas_pt?.dob}</div>
                </div>
                <div className="group -mx-2 flex h-7 w-full items-center gap-x-2 rounded-md px-2">
                  <div className="flex shrink-0 items-center gap-x-2">
                    <VenusAndMarsIcon className="size-4.5 text-neutral-500" />
                    <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">Gender:</Label>
                  </div>

                  <div className="grow truncate text-left text-xs text-neutral-900">
                    {data?.pas_pt?.gender_code}
                  </div>
                </div>
              </div>

              <div className="group -mx-2 flex h-8 w-full items-center gap-x-2 rounded-md px-2 hover:bg-neutral-100">
                <div className="flex shrink-0 items-center gap-x-2">
                  <FileIcon className="size-4.5 text-neutral-500" />
                  <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">Source:</Label>
                </div>

                <div className="grow truncate text-left text-xs text-neutral-900 capitalize">
                  {data?.type}
                </div>
              </div>

              <div className="group -mx-2 flex h-8 w-full items-center gap-x-2 rounded-md px-2 hover:bg-neutral-100">
                <div className="flex shrink-0 items-center gap-x-2">
                  <Calendar2Icon className="size-4.5 text-neutral-500" />
                  <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">
                    Referral Date:
                  </Label>
                </div>

                <div className="grow truncate text-left text-xs text-neutral-900">
                  {data?.date_recieved
                    ? format(new Date((data?.date_recieved as any) ?? null), 'dd/MM/yyyy')
                    : ''}
                </div>
              </div>
            </div>

            <div className="text-brand-500 mt-4 flex items-center gap-x-1">
              <a
                target="_blank"
                href={`/patients/${data?.patient_id}`}
                className="text-brand-500 cursor-point text-xs font-semibold capitalize underline"
              >
                Patient Profile
              </a>
              <ExternalLinkIcon className="h-3 w-3" />
            </div>

            <hr className="mt-6 mb-4 text-neutral-100" />

            <div className="my-4 text-xs leading-snug font-bold tracking-wide text-neutral-800 uppercase">
              Doctor Details
            </div>
            <div className="space-y-0.5">
              <Editable
                defaultValue={data?.requesting_doctor?.forename + ' ' + data?.requesting_doctor?.surname}
                // defaultValue={data?.requesting_doctor?.id}
                onSubmit={async (value) => {
                  await client.mutate({
                    mutation: mutateStudyRequestField('requesting_doctor_id', value),
                    refetchQueries: [getStudyRequest],
                    variables: {id: data?.id},
                  });
                }}
              >
                {({setIsEditing, previewValue, setPreviewValue}) => (
                  <>
                    <Editable.Trigger>
                      <div className="group -mx-2 flex h-7 w-full items-center gap-x-2 rounded-md px-2 hover:bg-neutral-100">
                        <div className="flex shrink-0 items-center gap-x-2">
                          <UserIcon className="size-4.5 text-neutral-500" />
                          <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">
                            Requesting Doctor:
                          </Label>
                        </div>

                        <div className="grow truncate text-left text-xs text-neutral-900 capitalize">
                          {previewValue as string}
                        </div>

                        <button
                          className="hidden cursor-pointer group-hover:flex hover:flex"
                          onClick={() => setIsEditing(true)}
                        >
                          <EditSquareIconly className="text-brand-500 h-4.5 w-4.5" />
                        </button>
                      </div>
                    </Editable.Trigger>
                    <Editable.Content className="mx-1 flex w-full items-center gap-x-4">
                      <Editable.Field
                        type="combobox"
                        items={doctorsList.items}
                        onInputChange={(value) => {
                          setPreviewValue('');
                          doctorsList.setFilterText(value);
                        }}
                        inputValue={(previewValue as string) || doctorsList.filterText}
                        itemIdKey="id"
                        itemValueKey="forename"
                        itemValueFormatter={formatDoctorName}
                        placeholder="Search Doctors"
                      />
                      <div className="flex items-center gap-x-2">
                        <Button
                          variant="outlined"
                          className="text-brand-500 focus-visible:outline-brand-500 cursor-pointer rounded-sm border p-1 focus-visible:outline-1"
                          aria-label="Cancel"
                          slot="cancel"
                        >
                          <CloseRemoveIconly className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outlined"
                          className="text-brand-500 focus-visible:outline-brand-500 cursor-pointer rounded-sm border p-1 focus-visible:outline-1"
                          aria-label="Submit"
                          slot="submit"
                        >
                          <CheckmarkIconly className="h-4 w-4" />
                        </Button>
                      </div>
                    </Editable.Content>
                  </>
                )}
              </Editable>

              <div className="group -mx-2 flex h-8 w-full items-center gap-x-2 rounded-md px-2 hover:bg-neutral-100">
                <div className="flex shrink-0 items-center gap-x-2">
                  <HashTagIcon className="size-4.5 text-neutral-500" />
                  <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">
                    Requesting Doctor Provider #:
                  </Label>
                </div>

                <div className="grow truncate text-left text-xs text-neutral-900 capitalize">
                  {data?.requesting_doctor?.provider_number}
                </div>
              </div>

              <Editable
                defaultValue={
                  data?.cc_doctor?.forename +
                  ' ' +
                  data?.cc_doctor?.surname +
                  ` (${data?.cc_doctor?.provider_number})`
                }
                onSubmit={async (value) => {
                  await client.mutate({
                    mutation: mutateStudyRequestField('report_cc_id', value),
                    refetchQueries: [getStudyRequest],
                    variables: {id: data?.id},
                  });
                }}
              >
                {({setIsEditing, previewValue, setPreviewValue}) => (
                  <>
                    <Editable.Trigger>
                      <div className="group -mx-2 flex h-7 w-full items-center gap-x-2 rounded-md px-2 hover:bg-neutral-100">
                        <div className="flex shrink-0 items-center gap-x-2">
                          <UserIcon className="size-4.5 text-neutral-500" />
                          <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">
                            CC Doctor:
                          </Label>
                        </div>

                        <div className="grow truncate text-left text-xs text-neutral-900 capitalize">
                          {previewValue as string}
                        </div>

                        <button
                          className="hidden cursor-pointer group-hover:flex hover:flex"
                          onClick={() => setIsEditing(true)}
                        >
                          <EditSquareIconly className="text-brand-500 h-4.5 w-4.5" />
                        </button>
                      </div>
                    </Editable.Trigger>
                    <Editable.Content className="mx-1 flex w-full items-center gap-x-4">
                      <Editable.Field
                        type="combobox"
                        items={doctorsCCList.items}
                        onInputChange={(value) => {
                          setPreviewValue('');
                          doctorsCCList.setFilterText(value);
                        }}
                        inputValue={(previewValue as string) || doctorsCCList.filterText}
                        itemIdKey="id"
                        itemValueKey="fullName"
                        itemValueFormatter={(item) => `${formatDoctorName(item)} (${item.provider_number})`}
                        placeholder="Search CC Doctors"
                      />
                      <div className="flex items-center gap-x-2">
                        <Button
                          variant="outlined"
                          className="text-brand-500 focus-visible:outline-brand-500 cursor-pointer rounded-sm border p-1 focus-visible:outline-1"
                          aria-label="Cancel"
                          slot="cancel"
                        >
                          <CloseRemoveIconly className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outlined"
                          className="text-brand-500 focus-visible:outline-brand-500 cursor-pointer rounded-sm border p-1 focus-visible:outline-1"
                          aria-label="Submit"
                          slot="submit"
                        >
                          <CheckmarkIconly className="h-4 w-4" />
                        </Button>
                      </div>
                    </Editable.Content>
                  </>
                )}
              </Editable>
            </div>

            <hr className="mt-6 mb-4 text-neutral-100" />

            <div className="my-4 text-xs leading-snug font-bold tracking-wide text-neutral-800 uppercase">
              Procedure Details
            </div>

            {data?.study_request_items && data.study_request_items.length > 0 ? (
              data.study_request_items.map((item) => (
                <div
                  key={item.id}
                  className="border-brand-100 my-4 space-y-0.5 rounded border p-4"
                >
                  <div className="group -mx-2 flex h-8 w-full items-center gap-x-2 rounded-md px-2 hover:bg-neutral-100">
                    <div className="flex shrink-0 items-center gap-x-2">
                      <UserIcon className="size-4.5 text-neutral-500" />
                      <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">
                        Procedure:
                      </Label>
                    </div>

                    <div className="grow truncate text-left text-xs text-neutral-900">
                      {item.procedure?.seq_number as any}. {item.procedure?.name}
                    </div>

                    <button className="text-brand-500 cursor-pointer text-xs font-semibold">edit</button>
                  </div>

                  <div className="group -mx-2 flex h-8 w-full items-center gap-x-2 rounded-md px-2 hover:bg-neutral-100">
                    <div className="flex shrink-0 items-center gap-x-2">
                      <UserIcon className="size-4.5 text-neutral-500" />
                      <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">
                        Provisional Date:
                      </Label>
                    </div>

                    <div className="grow truncate text-left text-xs text-neutral-900">
                      {item.provisional_procedure_date
                        ? format(new Date(item.provisional_procedure_date as any), 'dd MMM, yyyy')
                        : 'Not specified'}
                    </div>
                  </div>

                  <div className="group -mx-2 flex h-8 w-full items-center gap-x-2 rounded-md px-2 hover:bg-neutral-100">
                    <div className="flex shrink-0 items-center gap-x-2">
                      <UserIcon className="size-4.5 text-neutral-500" />
                      <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">Status:</Label>
                    </div>

                    <div className="grow truncate text-left text-xs text-neutral-900 capitalize">
                      {item.status}
                    </div>
                  </div>

                  {item.note && (
                    <div className="group -mx-2 flex flex-col gap-y-2 rounded-md px-2 py-1 hover:bg-neutral-100">
                      <div className="flex shrink-0 items-center gap-x-2">
                        <UserIcon className="size-4.5 text-neutral-500" />
                        <Label className="mb-0 text-xs font-normal text-neutral-700 capitalize">
                          Test/Clinical Notes:
                        </Label>
                      </div>

                      <div className="grow text-left text-xs text-neutral-900">{item.note}</div>
                    </div>
                  )}

                  {item.status !== 'accepted' && item.status !== 'rejected' && (
                    <div className="mt-4 flex items-center justify-end gap-x-2">
                      <Button
                        variant="outlined"
                        color="danger"
                        size="small"
                        className="h-7 rounded-md"
                        onPress={() => handleReject(item.id)}
                      >
                        <X data-slot="icon" />
                        Reject
                      </Button>
                      <Button
                        variant="outlined"
                        color="success"
                        size="small"
                        className="h-7 rounded-md"
                        onPress={() => handleAccept(item.id)}
                      >
                        <CheckIcon data-slot="icon" />
                        Accept
                      </Button>
                    </div>
                  )}

                  {item.status === 'accepted' && (
                    <div className="mt-4 flex items-center justify-end">
                      <div className="flex items-center gap-x-1 text-xs font-semibold text-[#16A34A]">
                        <CheckIcon className="h-4 w-4" />
                        Accepted
                      </div>
                    </div>
                  )}

                  {item.status === 'rejected' && (
                    <div className="mt-4 flex items-center justify-end">
                      <div className="flex items-center gap-x-1 text-xs font-semibold text-[#EF4444]">
                        <X className="h-4 w-4" />
                        Rejected
                      </div>
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div className="my-4 text-center text-sm text-neutral-500">
                No procedures found for this referral.
              </div>
            )}
          </div>
        </Form>
        </FormProvider>
      </Dialog>
    </Modal>
  );
}
